<!--
  订单管理页面 - ShopOrder.vue
  基于接口文档实现完整的订单管理功能
  包含：订单列表、搜索筛选、导出、详情查看、排行榜等功能
-->

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <TopNav title="订单管理" />

    <div class="content-container">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ censusData.summary.totalOrderCount || 0 }}</div>
              <div class="stats-label">订单总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ censusData.summary.totalAmount || '0.00' }}</div>
              <div class="stats-label">总支付金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ censusData.summary.todayOrderCount || 0 }}</div>
              <div class="stats-label">今日订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ censusData.summary.todayAmount || '0.00' }}</div>
              <div class="stats-label">今日金额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计卡片 -->
      <el-row :gutter="20" class="detail-stats-cards">
        <el-col :span="12">
          <el-card class="detail-stats-card">
            <template #header>
              <div class="card-header">
                <span>一口价模式统计</span>
              </div>
            </template>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.pendingPayFixedCount }}</div>
                <div class="stat-label">待支付</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.paidUnacceptedCount }}</div>
                <div class="stat-label">已支付未接单</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.acceptedWaitArriveCount }}</div>
                <div class="stat-label">已接单待上门</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.waitScheduleCount }}</div>
                <div class="stat-label">待预约</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.waitServiceCount }}</div>
                <div class="stat-label">待服务</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.servicingCount }}</div>
                <div class="stat-label">服务中</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.finishedCount }}</div>
                <div class="stat-label">已完成</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.fixed.cancelCount }}</div>
                <div class="stat-label">取消订单</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="detail-stats-card">
            <template #header>
              <div class="card-header">
                <span>报价模式统计</span>
              </div>
            </template>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.waitQuoteCount }}</div>
                <div class="stat-label">待报价</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.quotedNotChosenCount }}</div>
                <div class="stat-label">已报价未选择</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.pendingPayQuoteCount }}</div>
                <div class="stat-label">待支付</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.acceptedWaitArriveCount }}</div>
                <div class="stat-label">已接单待上门</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.waitScheduleCount }}</div>
                <div class="stat-label">待预约</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.waitServiceCount }}</div>
                <div class="stat-label">待服务</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.servicingCount }}</div>
                <div class="stat-label">服务中</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.finishedCount }}</div>
                <div class="stat-label">已完成</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ censusData.quote.cancelCount }}</div>
                <div class="stat-label">取消订单</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 第一行搜索条件 -->
              <el-form-item label="订单号" prop="orderCode">
                <el-input size="default" v-model="searchForm.orderCode" placeholder="请输入订单号" clearable
                  style="width: 200px" />
              </el-form-item>

              <el-form-item label="商品名称" prop="goodsName">
                <el-input size="default" v-model="searchForm.goodsName" placeholder="请输入商品名称" clearable
                  style="width: 200px" />
              </el-form-item>

              <el-form-item label="师傅姓名" prop="coachName">
                <el-input size="default" v-model="searchForm.coachName" placeholder="请输入师傅姓名" clearable
                  style="width: 200px" />
              </el-form-item>

              <el-form-item label="订单状态" prop="payType">
                <el-select size="default" v-model="searchForm.payType" placeholder="请选择订单状态" clearable
                  style="width: 180px">
                  <el-option label="待报价" :value="-3" />
                  <el-option label="已报价(未选择报价)" :value="-2" />
                  <el-option label="取消订单" :value="-1" />
                  <el-option label="待支付" :value="1" />
                  <el-option label="已支付，师傅未接单" :value="2" />
                  <el-option label="师傅已接单待上门" :value="3" />
                  <el-option label="待预约" :value="4" />
                  <el-option label="待服务（已上门）" :value="5" />
                  <el-option label="服务中（开始服务）" :value="6" />
                  <el-option label="已完成" :value="7" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 第二行搜索条件 -->
              <el-form-item label="订单类型" prop="type">
                <el-select size="default" v-model="searchForm.type" placeholder="请选择订单类型" clearable
                  style="width: 150px">
                  <el-option label="一口价模式" :value="0" />
                  <el-option label="报价模式" :value="1" />
                </el-select>
              </el-form-item>

              <el-form-item label="地址" prop="address">
                <el-input size="default" v-model="searchForm.address" placeholder="请输入地址" clearable
                  style="width: 200px" />
              </el-form-item>

              <el-form-item label="选择城市" prop="cityId">
                <el-cascader size="default" v-model="searchForm.cityId" :options="cityOptions" :props="cascaderProps"
                  placeholder="请选择城市" clearable style="width: 200px" @change="handleCityChange" />
              </el-form-item>

              <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker size="default" v-model="timeRange" type="datetimerange" range-separator="至"
                  start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px" @change="handleTimeRangeChange" />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <LbButton size="default" type="primary" icon="Search" @click="handleSearch" :loading="loading">
                  搜索
                </LbButton>
                <LbButton size="default" icon="RefreshLeft" @click="handleReset">
                  重置
                </LbButton>
                <LbButton size="default" type="success" icon="Download" @click="handleExport" :loading="exportLoading">
                  导出
                </LbButton>
                <LbButton size="default" type="warning" icon="TrendCharts" @click="showRankDialog = true">
                  排行榜
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table v-loading="loading" :data="tableData" :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontSize: '16px',
          fontWeight: '600'
        }" :cell-style="{
          fontSize: '14px',
          padding: '12px 8px'
        }" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="orderCode" label="订单号" min-width="180" />
          <el-table-column prop="goodsName" label="商品名称" width="150" />
          <el-table-column prop="num" label="数量" width="80" align="center" />
          <el-table-column prop="payPrice" label="支付金额" width="100" align="center">
            <template #default="{ row }">
              <span class="price-text">¥{{ row.payPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="用户昵称" width="120" />
          <el-table-column prop="phone" label="用户手机" width="160" />
          <el-table-column prop="coachName" label="师傅姓名" width="120" />
          <el-table-column prop="coachMobile" label="师傅手机" width="160" />
          <el-table-column prop="payType" label="订单状态" min-width="250" align="center">
            <template #default="{ row }">
              <el-tag :type="getPayTypeTagType(row.payType)">
                {{ getPayTypeText(row.payType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="订单类型" width="180" align="center">
            <template #default="{ row }">
              <el-tag :type="row.type === 0 ? 'warning' : 'primary'">
                {{ row.type === 0 ? '一口价模式' : '报价模式' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" min-width="300" align="center" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton size="default" type="primary" @click="handleViewDetail(scope.row)">
                  详情
                </LbButton>
                <LbButton size="default" type="success" @click="handleViewQuoteDetail(scope.row)"
                  v-if="scope.row.quotedPriceVos === 1">
                  报价详情
                </LbButton>
                <LbButton size="default" type="warning" @click="handleViewDiffPriceDetail(scope.row)"
                  v-if="scope.row.orderDiffPrices === 1">
                  补差价详情
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage :page="searchForm.pageNum" :page-size="searchForm.pageSize" :total="total"
        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>

    <!-- 订单详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="订单详情" width="1200px" :close-on-click-modal="false"
      class="order-detail-dialog">
      <div v-if="orderDetail" class="detail-content">
        <!-- 订单概览卡片 -->
        <div class="order-overview-card">
          <div class="order-header">
            <div class="order-number">
              <span class="label">订单号：</span>
              <span class="value">{{ orderDetail.orderCode || '-' }}</span>
            </div>
            <div class="order-status">
              <el-tag :type="getPayTypeTagType(orderDetail.payType)" size="large">
                {{ getPayTypeText(orderDetail.payType) }}
              </el-tag>
            </div>
          </div>
          <div class="order-meta">
            <div class="meta-item">
              <i class="el-icon-time"></i>
              <span>{{ orderDetail.createTime || '-' }}</span>
            </div>
            <div class="meta-item">
              <i class="el-icon-money"></i>
              <span class="price-highlight">¥{{ orderDetail.payPrice || '0.00' }}</span>
            </div>
            <div class="meta-item">
              <el-tag :type="orderDetail.type === 0 ? 'warning' : 'primary'" size="small">
                {{ orderDetail.type === 0 ? '一口价模式' : '报价模式' }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 主要信息区域 - 使用两列布局 -->
        <el-row :gutter="24" class="main-info-row">
          <!-- 左列 -->
          <el-col :span="12">
            <!-- 用户信息 -->
            <div class="info-card">
              <div class="card-header">
                <i class="el-icon-user"></i>
                <h4>用户信息</h4>
              </div>
              <div class="card-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">用户ID</span>
                    <span class="value">{{ orderDetail.userId || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">下单人</span>
                    <span class="value">{{ orderDetail.addressInfo?.userName || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联系方式</span>
                    <span class="value">{{ orderDetail.addressInfo?.mobile || '-' }}</span>
                  </div>
                  <div class="info-item full-width">
                    <span class="label">项目地址</span>
                    <span class="value">{{ orderDetail.addressInfo?.addressInfo || '-' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 订单信息 -->
            <div class="info-card">
              <div class="card-header">
                <i class="el-icon-document"></i>
                <h4>订单信息</h4>
              </div>
              <div class="card-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">项目时间</span>
                    <span class="value">{{ formatServiceTime(orderDetail.startTime, orderDetail.endTime) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">项目费用</span>
                    <span class="value price-text">¥{{ orderDetail.servicePrice || '0.00' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">支付方式</span>
                    <span class="value">{{ getPayMethodText(orderDetail.payType) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">支付时间</span>
                    <span class="value">{{ orderDetail.payTime || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">微信交易号</span>
                    <span class="value">{{ orderDetail.transactionId || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">退款状态</span>
                    <el-tag :type="orderDetail.refundStatus === 0 ? 'success' : 'warning'" size="small">
                      {{ orderDetail.refundStatus === 0 ? '未退款' : '已退款' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 右列 -->
          <el-col :span="12">
            <!-- 师傅信息 -->
            <div class="info-card" v-if="orderDetail.coachInfo">
              <div class="card-header">
                <i class="el-icon-user-solid"></i>
                <h4>师傅信息</h4>
              </div>
              <div class="card-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">师傅ID</span>
                    <span class="value">{{ orderDetail.coachInfo.id || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">师傅姓名</span>
                    <span class="value">{{ orderDetail.coachInfo.coachName || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联系方式</span>
                    <span class="value">{{ orderDetail.coachInfo.mobileEncry || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">师傅等级</span>
                    <span class="value">{{ orderDetail.coachInfo.labelName || '-' }}</span>
                  </div>
                  <div class="info-item full-width">
                    <span class="label">师傅地址</span>
                    <span class="value">{{ orderDetail.coachInfo.address || '-' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 项目内容 -->
        <div class="info-card" v-if="orderDetail.orderGoods && orderDetail.orderGoods.length > 0">
          <div class="card-header">
            <i class="el-icon-goods"></i>
            <h4>项目内容</h4>
          </div>
          <div class="card-content">
            <div v-for="(goods, index) in orderDetail.orderGoods" :key="index" class="goods-item-enhanced">
              <div class="goods-main-info">
                <div class="goods-image-wrapper">
                  <el-image :src="goods.goodsCover" class="goods-cover-image" fit="cover"
                    :preview-src-list="[goods.goodsCover]" :z-index="10000" :hide-on-click-modal="false"
                    :preview-teleported="true" lazy :infinite="false" :close-on-press-escape="true">
                    <template #placeholder>
                      <div class="image-placeholder">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                        <span>加载中...</span>
                      </div>
                    </template>
                    <template #error>
                      <div class="image-error">
                        <el-icon>
                          <Picture />
                        </el-icon>
                        <span>加载失败</span>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div class="goods-basic-info">
                  <div class="goods-name-enhanced">{{ goods.goodsName }}</div>
                  <div class="goods-meta">
                    <span class="goods-quantity-enhanced">数量：{{ goods.num }}</span>
                    <span class="goods-price-enhanced">¥{{ goods.price }}</span>
                  </div>
                </div>
              </div>

              <!-- 项目配置信息 -->
              <div v-if="goods.priceSetting && goods.priceSetting.length > 0" class="goods-settings-enhanced">
                <div class="settings-title">配置详情</div>
                <div class="settings-grid">
                  <div v-for="(setting, settingIndex) in goods.priceSetting" :key="settingIndex"
                    class="setting-item-enhanced">
                    <span class="setting-label-enhanced">{{ setting.problemDesc }}</span>
                    <div class="setting-value-enhanced">
                      <!-- 如果是图片URL，显示图片 -->
                      <template v-if="setting.val && isImageUrl(setting.val)">
                        <div class="setting-image-enhanced">
                          <div class="setting-image-wrapper">
                            <el-image :src="setting.val" class="setting-image-display" fit="cover"
                              :preview-src-list="[setting.val]" :z-index="10000" :hide-on-click-modal="false"
                              :preview-teleported="true" lazy :infinite="false" :close-on-press-escape="true">
                              <template #placeholder>
                                <div class="setting-image-placeholder">
                                  <el-icon class="is-loading">
                                    <Loading />
                                  </el-icon>
                                  <span>加载中...</span>
                                </div>
                              </template>
                              <template #error>
                                <div class="setting-image-error">
                                  <el-icon>
                                    <Picture />
                                  </el-icon>
                                  <span>加载失败</span>
                                  <el-button type="text" size="small"
                                    @click="retryLoadImage(setting.val)">重试</el-button>
                                </div>
                              </template>
                            </el-image>
                            <div class="setting-image-overlay">
                              <div class="setting-image-actions">
                                <el-button type="primary" size="small" circle
                                  @click.stop="handleImagePreview(setting.val)" :title="'预览图片'">
                                  <el-icon>
                                    <ZoomIn />
                                  </el-icon>
                                </el-button>
                                <el-button type="success" size="small" circle
                                  @click.stop="downloadImage(setting.val, 0)" :title="'下载图片'">
                                  <el-icon>
                                    <Download />
                                  </el-icon>
                                </el-button>
                                <el-button type="info" size="small" circle @click.stop="copyToClipboard(setting.val)"
                                  :title="'复制链接'">
                                  <el-icon>
                                    <DocumentCopy />
                                  </el-icon>
                                </el-button>
                              </div>
                            </div>
                          </div>
                          <div class="setting-image-info">
                            <span class="image-label">{{ getImageTypeLabel(setting.val) }}</span>
                            <span class="image-url-preview" @click="copyToClipboard(setting.val)" :title="setting.val">
                              {{ setting.val.length > 40 ? setting.val.substring(0, 40) + '...' : setting.val }}
                            </span>
                          </div>
                        </div>
                      </template>
                      <!-- 其他字段正常显示文本 -->
                      <template v-else>
                        <span class="setting-text">{{ setting.val }}</span>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="goods-summary">
              <span class="summary-text">合计数量：{{ getTotalQuantity() }}</span>
            </div>
          </div>
        </div>

        <!-- 物品图片 -->
        <div class="info-card" v-if="orderDetail.goodsImages && orderDetail.goodsImages.length > 0">
          <div class="card-header">
            <i class="el-icon-picture"></i>
            <h4>物品图片</h4>
            <div class="image-count-badge">
              <span>{{ orderDetail.goodsImages.length }} 张图片</span>
            </div>
          </div>
          <div class="card-content">
            <div class="images-gallery-enhanced">
              <div v-for="(imageUrl, index) in orderDetail.goodsImages" :key="index" class="gallery-item-enhanced">
                <div class="image-wrapper">
                  <el-image :src="imageUrl" class="gallery-image-enhanced" fit="cover"
                    :preview-src-list="orderDetail.goodsImages" :initial-index="index" :z-index="10000"
                    :hide-on-click-modal="false" :preview-teleported="true" lazy :infinite="false"
                    :close-on-press-escape="true">
                    <template #placeholder>
                      <div class="image-placeholder">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                        <span>加载中...</span>
                      </div>
                    </template>
                    <template #error>
                      <div class="image-error">
                        <el-icon>
                          <Picture />
                        </el-icon>
                        <span>加载失败</span>
                        <el-button type="text" size="small" @click="retryLoadImage(imageUrl)">重试</el-button>
                      </div>
                    </template>
                  </el-image>
                  <div class="image-overlay">
                    <div class="image-actions">
                      <el-button type="primary" size="small" circle @click.stop="handleImagePreview(imageUrl)"
                        :title="'预览图片'">
                        <el-icon>
                          <ZoomIn />
                        </el-icon>
                      </el-button>
                      <el-button type="success" size="small" circle @click.stop="downloadImage(imageUrl, index)"
                        :title="'下载图片'">
                        <el-icon>
                          <Download />
                        </el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
                <div class="image-info">
                  <span class="image-index">{{ index + 1 }}</span>
                  <span class="image-size" v-if="getImageSize(imageUrl)">{{ getImageSize(imageUrl) }}</span>
                </div>
              </div>
            </div>

            <!-- 图片操作工具栏 -->
            <div class="image-toolbar">
              <el-button type="primary" size="small" @click="previewAllImages">
                <i class="el-icon-view"></i>
                预览所有图片
              </el-button>
              <el-button type="success" size="small" @click="downloadAllImages">
                <i class="el-icon-download"></i>
                下载所有图片
              </el-button>
              <el-button type="info" size="small" @click="copyImageUrls">
                <i class="el-icon-document-copy"></i>
                复制图片链接
              </el-button>
            </div>
          </div>
        </div>

        <!-- 单独的物品图片（兼容旧数据） -->
        <div class="info-card" v-if="orderDetail.goodsImage && !orderDetail.goodsImages">
          <div class="card-header">
            <i class="el-icon-picture"></i>
            <h4>物品图片</h4>
          </div>
          <div class="card-content">
            <div class="single-image-container">
              <div class="single-image-wrapper">
                <el-image :src="orderDetail.goodsImage" class="single-image" fit="cover"
                  :preview-src-list="[orderDetail.goodsImage]" :z-index="10000" :hide-on-click-modal="false"
                  :preview-teleported="true" lazy :infinite="false" :close-on-press-escape="true">
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon class="is-loading">
                        <Loading />
                      </el-icon>
                      <span>加载中...</span>
                    </div>
                  </template>
                  <template #error>
                    <div class="image-error">
                      <el-icon>
                        <Picture />
                      </el-icon>
                      <span>加载失败</span>
                      <el-button type="text" size="small" @click="retryLoadImage(orderDetail.goodsImage)">重试</el-button>
                    </div>
                  </template>
                </el-image>
                <div class="single-image-actions">
                  <el-button type="primary" size="small" @click.stop="handleImagePreview(orderDetail.goodsImage)">
                    <el-icon>
                      <ZoomIn />
                    </el-icon>
                    预览
                  </el-button>
                  <el-button type="success" size="small" @click.stop="downloadImage(orderDetail.goodsImage, 0)">
                    <el-icon>
                      <Download />
                    </el-icon>
                    下载
                  </el-button>
                </div>
              </div>
              <div class="single-image-url">
                <span class="url-label">图片链接：</span>
                <span class="url-text" @click="copyToClipboard(orderDetail.goodsImage)">{{ orderDetail.goodsImage
                  }}</span>
                <el-button type="text" size="mini" @click="copyToClipboard(orderDetail.goodsImage)">
                  <i class="el-icon-document-copy"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <LbButton @click="detailDialogVisible = false">关闭</LbButton>
      </template>
    </el-dialog>

    <!-- 排行榜弹窗 -->
    <el-dialog v-model="showRankDialog" title="排行榜统计" width="1000px" :close-on-click-modal="false">
      <div class="rank-container">
        <!-- 排行榜筛选条件 -->
        <el-form :model="rankForm" :inline="true" class="rank-form">
          <el-form-item label="排行数量">
            <el-select v-model="rankForm.top" placeholder="选择排行数量" style="width: 120px">
              <el-option label="前5名" :value="5" />
              <el-option label="前10名" :value="10" />
              <el-option label="前20名" :value="20" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker v-model="rankTimeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
              end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px" @change="handleRankTimeRangeChange" />
          </el-form-item>
          <el-form-item>
            <LbButton type="primary" @click="loadRankData" :loading="rankLoading">
              查询排行榜
            </LbButton>
          </el-form-item>
        </el-form>

        <!-- 排行榜标签页 -->
        <el-tabs v-model="activeRankTab" @tab-click="handleRankTabClick">
          <!-- 师傅收入排行榜 -->
          <el-tab-pane label="师傅收入排行榜" name="coachIncome">
            <el-table v-loading="rankLoading" :data="coachIncomeRank" style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
              <el-table-column type="index" label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <el-tag :type="$index < 3 ? 'danger' : 'info'" effect="dark">
                    {{ $index + 1 }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="coachId" label="师傅ID" width="100" align="center" />
              <el-table-column prop="coachName" label="师傅姓名" width="120">
                <template #default="{ row }">
                  {{ row.coachName || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="coachMobile" label="师傅手机" width="130">
                <template #default="{ row }">
                  {{ row.coachMobile || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="totalIncome" label="总收入" width="120" align="center">
                <template #default="{ row }">
                  <span class="income-text">¥{{ row.totalIncome }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="orderCount" label="订单数量" min-width="100" align="center" />
            </el-table>
          </el-tab-pane>

          <!-- 师傅跑单排行榜 -->
          <el-tab-pane label="师傅跑单排行榜" name="coachCancel">
            <el-table v-loading="rankLoading" :data="coachCancelRank" style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
              <el-table-column type="index" label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <el-tag :type="$index < 3 ? 'danger' : 'info'" effect="dark">
                    {{ $index + 1 }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="coachId" label="师傅ID" width="100" align="center" />
              <el-table-column prop="coachName" label="师傅姓名" width="120">
                <template #default="{ row }">
                  {{ row.coachName || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="coachMobile" label="师傅手机" min-width="130">
                <template #default="{ row }">
                  {{ row.coachMobile || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="cancelCount" label="跑单次数" width="100" align="center" />
              <el-table-column prop="cancelRate" label="跑单率" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.cancelRate > 30 ? 'danger' : row.cancelRate > 15 ? 'warning' : 'success'">
                    {{ row.cancelRate }}%
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 用户跑单排行榜 -->
          <el-tab-pane label="用户跑单排行榜" name="userCancel">
            <el-table v-loading="rankLoading" :data="userCancelRank" style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
              <el-table-column type="index" label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <el-tag :type="$index < 3 ? 'danger' : 'info'" effect="dark">
                    {{ $index + 1 }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="userId" label="用户ID" width="100" align="center" />
              <el-table-column prop="nickName" label="用户昵称" width="120">
                <template #default="{ row }">
                  {{ row.nickName || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="phone" label="用户手机" min-width="130">
                <template #default="{ row }">
                  {{ row.phone || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="cancelCount" label="跑单次数" width="100" align="center" />
              <el-table-column prop="cancelRate" label="跑单率" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.cancelRate > 30 ? 'danger' : row.cancelRate > 15 ? 'warning' : 'success'">
                    {{ row.cancelRate }}%
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <LbButton @click="showRankDialog = false">关闭</LbButton>
      </template>
    </el-dialog>

    <!-- 报价详情弹窗 -->
    <el-dialog v-model="quoteDetailDialogVisible" title="报价详情" width="1000px" :close-on-click-modal="false">
      <div class="quote-detail-container">
        <!-- 分页控制 -->
        <div class="quote-pagination-container">
          <LbPage :page="quoteForm.pageNum" :page-size="quoteForm.pageSize" :total="quoteTotal"
            @handleSizeChange="handleQuoteSizeChange" @handleCurrentChange="handleQuoteCurrentChange" />
        </div>

        <!-- 报价列表表格 -->
        <el-table v-loading="quoteLoading" :data="quoteDetailList" style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
          <el-table-column prop="id" label="报价记录ID" width="100" align="center" />
          <el-table-column prop="coachId" label="师傅ID" width="80" align="center" />
          <el-table-column prop="coachName" label="师傅名称" width="120" />
          <el-table-column prop="mobile" label="师傅手机" width="130" />
          <el-table-column prop="labelName" label="师傅等级" width="100" />
          <el-table-column prop="price" label="报价金额" width="100" align="center">
            <template #default="{ row }">
              <span class="price-text">¥{{ row.price }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="服务次数" width="80" align="center" />
          <el-table-column prop="workTime" label="工时" width="80" align="center" />
          <el-table-column prop="city" label="所在城市" width="100" />
          <el-table-column prop="address" label="详细地址" min-width="200" />
          <el-table-column prop="priceCreateTime" label="报价时间" width="160" />
          <el-table-column label="师傅头像" width="80" align="center">
            <template #default="{ row }">
              <el-image v-if="row.selfImg" :src="row.selfImg" style="width: 40px; height: 40px; border-radius: 50%;"
                fit="cover" :preview-src-list="[row.selfImg]" />
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="sex" label="性别" width="60" align="center">
            <template #default="{ row }">
              <el-tag :type="row.sex === 0 ? 'primary' : 'success'" size="small">
                {{ row.sex === 0 ? '女' : '男' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="text" label="自我介绍" min-width="150">
            <template #default="{ row }">
              <span>{{ row.text || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <LbButton @click="quoteDetailDialogVisible = false">关闭</LbButton>
      </template>
    </el-dialog>

    <!-- 补差价详情弹窗 -->
    <el-dialog v-model="diffPriceDialogVisible" title="补差价详情" width="1200px" :close-on-click-modal="false">
      <div class="diff-price-container">
        <!-- 分页控制 -->
        <div class="diff-price-pagination-container">
          <LbPage :page="diffPriceForm.pageNum" :page-size="diffPriceForm.pageSize" :total="diffPriceTotal"
            @handleSizeChange="handleDiffPriceSizeChange" @handleCurrentChange="handleDiffPriceCurrentChange" />
        </div>

        <!-- 补差价列表表格 -->
        <el-table v-loading="diffPriceLoading" :data="diffPriceDetailList" style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
          <el-table-column prop="id" label="主键ID" width="80" align="center" />
          <el-table-column prop="orderId" label="原订单ID" width="100" align="center" />
          <el-table-column prop="userId" label="用户ID" width="80" align="center" />
          <el-table-column prop="diffCode" label="补差价订单编号" width="180" />
          el-table-column prop="transactionId" label="第三方交易单号" width="200" />
          <el-table-column prop="diffAmount" label="申请差价金额" width="120" align="center">
            <template #default="{ row }">
              <span class="price-text">¥{{ row.diffAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="creditPrice" label="师傅到账金额" width="120" align="center">
            <template #default="{ row }">
              <span class="income-text">¥{{ row.creditPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="applyPrice" label="用户支付金额" width="120" align="center">
            <template #default="{ row }">
              <span class="price-text">¥{{ row.applyPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reasonType" label="差价原因类型" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="row.reasonType === 1 ? 'warning' : 'info'" size="small">
                {{ row.reasonType === 1 ? '配件不符合' : '其他' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="reasonDetail" label="差价原因详情" min-width="150" />
          <el-table-column label="配件图片" width="80" align="center">
            <template #default="{ row }">
              <el-image v-if="row.partsImgs" :src="row.partsImgs" style="width: 40px; height: 40px; border-radius: 4px;"
                fit="cover" :preview-src-list="[row.partsImgs]" />
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getDiffPriceStatusType(row.status)" size="small">
                {{ getDiffPriceStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="coachId" label="师傅ID" width="80" align="center" />
          <el-table-column prop="refusedText" label="拒绝原因" min-width="120">
            <template #default="{ row }">
              <span>{{ row.refusedText || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="refundType" label="退款状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getRefundStatusType(row.refundType)" size="small">
                {{ getRefundStatusText(row.refundType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdTime" label="创建时间" width="160" />
          <el-table-column prop="updatedTime" label="更新时间" width="160" />
        </el-table>
      </div>
      <template #footer>
        <LbButton @click="diffPriceDialogVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Picture, ZoomIn, Download, DocumentCopy } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const rankLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const timeRange = ref([])
const rankTimeRange = ref([])

// 统计数据
const censusData = ref({
  fixed: {
    cancelCount: 0,
    acceptedWaitArriveCount: 0,
    waitScheduleCount: 0,
    waitServiceCount: 0,
    servicingCount: 0,
    finishedCount: 0,
    pendingPayFixedCount: 0,
    paidUnacceptedCount: 0,
    waitQuoteCount: 0,
    quotedNotChosenCount: 0,
    pendingPayQuoteCount: 0
  },
  quote: {
    cancelCount: 0,
    acceptedWaitArriveCount: 0,
    waitScheduleCount: 0,
    waitServiceCount: 0,
    servicingCount: 0,
    finishedCount: 0,
    pendingPayFixedCount: 0,
    paidUnacceptedCount: 0,
    waitQuoteCount: 0,
    quotedNotChosenCount: 0,
    pendingPayQuoteCount: 0
  },
  summary: {
    totalOrderCount: 0,
    todayOrderCount: 0,
    totalAmount: 0,
    todayAmount: 0
  }
})

// 弹窗控制
const detailDialogVisible = ref(false)
const showRankDialog = ref(false)
const orderDetail = ref(null)

// 报价详情弹窗控制
const quoteDetailDialogVisible = ref(false)
const quoteLoading = ref(false)
const quoteDetailList = ref([])
const quoteTotal = ref(0)
const currentOrderId = ref(null)

// 补差价详情弹窗控制
const diffPriceDialogVisible = ref(false)
const diffPriceLoading = ref(false)
const diffPriceDetailList = ref([])
const diffPriceTotal = ref(0)

// 排行榜相关
const activeRankTab = ref('coachIncome')
const coachIncomeRank = ref([])
const coachCancelRank = ref([])
const userCancelRank = ref([])

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true, // 返回完整路径，便于显示省市区
  checkStrictly: false, // 只能选择叶子节点（区县级别）
  expandTrigger: 'hover' // 鼠标悬停展开
}

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  orderCode: '',
  goodsName: '',
  coachName: '',
  payType: null,
  type: null,
  startTime: '',
  endTime: '',
  address: '',
  cityId: []
})

// 排行榜表单
const rankForm = reactive({
  top: 10,
  startTime: '',
  endTime: ''
})

// 报价详情表单
const quoteForm = reactive({
  pageNum: 1,
  pageSize: 10,
  id: ''
})

// 补差价详情表单
const diffPriceForm = reactive({
  pageNum: 1,
  pageSize: 10,
  id: ''
})

// 搜索表单引用
const searchFormRef = ref(null)

/**
 * 获取支付方式文本
 */
const getPayTypeText = (payType) => {
  const payTypeMap = {
    '-3': '待报价',
    '-2': '已报价(未选择报价)',
    '-1': '取消订单',
    '1': '待支付',
    '2': '已支付，师傅未接单',
    '3': '师傅已接单待上门',
    '4': '待预约',
    '5': '待服务（已上门）',
    '6': '服务中（开始服务）',
    '7': '已完成'
  }
  return payTypeMap[payType] || '未知'
}

/**
 * 获取支付方式标签类型
 */
const getPayTypeTagType = (payType) => {
  const typeMap = {
    '-3': 'info',      // 待报价
    '-2': 'warning',   // 已报价(未选择报价)
    '-1': 'danger',    // 取消订单
    '1': 'warning',    // 待支付
    '2': 'primary',    // 已支付，师傅未接单
    '3': 'primary',    // 师傅已接单待上门
    '4': 'warning',    // 待预约
    '5': 'success',    // 待服务（已上门）
    '6': 'success',    // 服务中（开始服务）
    '7': 'success'     // 已完成
  }
  return typeMap[payType] || 'info'
}

/**
 * 获取支付方式文本（用于显示具体的支付方式）
 */
const getPayMethodText = (payType) => {
  // 根据实际的支付类型返回支付方式
  if (payType === 1 || payType === '1') {
    return '微信支付'
  } else if (payType === 2 || payType === '2') {
    return '支付宝'
  } else if (payType === 7 || payType === '7') {
    return '余额支付'
  }
  return '微信支付' // 默认显示微信支付
}

/**
 * 格式化服务时间
 */
const formatServiceTime = (startTime, endTime) => {
  if (!startTime || !endTime) return '-'

  const start = new Date(startTime)
  const end = new Date(endTime)

  const formatTime = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  }

  return `${formatTime(start)} - ${formatTime(end)}`
}

/**
 * 计算商品总数量
 */
const getTotalQuantity = () => {
  if (!orderDetail.value || !orderDetail.value.orderGoods) return 0

  return orderDetail.value.orderGoods.reduce((total, goods) => {
    return total + (goods.num || 0)
  }, 0)
}

/**
 * 处理时间范围变化
 */
const handleTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    searchForm.startTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

/**
 * 城市变更处理
 */
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)

  if (value && value.length > 0) {
    // 取最后一级的城市ID（区县级别）
    const selectedCityId = value[value.length - 1]
    console.log('🏙️ 选中的城市ID:', selectedCityId)

    // 可以在这里添加额外的处理逻辑，比如获取该城市的其他信息
    // 城市选择变更时，cityId 会自动更新到 searchForm.cityId
  } else {
    console.log('🏙️ 清空城市选择')
  }
}

/**
 * 获取城市数据（用于级联选择器）
 */
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await proxy.$api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

/**
 * 处理排行榜时间范围变化
 */
const handleRankTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    rankForm.startTime = value[0]
    rankForm.endTime = value[1]
  } else {
    rankForm.startTime = ''
    rankForm.endTime = ''
  }
}

/**
 * 加载统计数据
 */
const loadCensusData = async () => {
  try {
    console.log('📊 开始加载订单统计数据')

    // 构建统计数据请求参数
    const censusParams = {}

    // 添加城市参数
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      censusParams.cityId = searchForm.cityId.join(',')
      console.log('📊 统计数据包含城市参数:', censusParams.cityId)
    }

    const response = await proxy.$api.shop.orderCensus(censusParams)
    console.log('📊 订单统计响应:', response)

    if (response.code === '200') {
      censusData.value = response.data
      console.log('✅ 订单统计数据加载成功:', censusData.value)
    } else {
      console.error('❌ 获取统计数据失败:', response.msg)
      ElMessage.error(response.msg || '获取统计数据失败')
    }
  } catch (error) {
    console.error('❌ 加载统计数据异常:', error)
    ElMessage.error('获取统计数据失败')
  }
}

/**
 * 加载订单列表
 */
const loadOrderList = async () => {
  try {
    loading.value = true
    console.log('🔍 开始加载订单列表，参数:', searchForm)

    // 构建请求参数，只传递有值的参数
    const requestParams = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 只有在搜索时才添加其他参数
    if (searchForm.orderCode) requestParams.orderCode = searchForm.orderCode
    if (searchForm.goodsName) requestParams.goodsName = searchForm.goodsName
    if (searchForm.coachName) requestParams.coachName = searchForm.coachName
    if (searchForm.payType !== null) requestParams.payType = searchForm.payType
    if (searchForm.type !== null) requestParams.type = searchForm.type
    if (searchForm.startTime) requestParams.startTime = searchForm.startTime
    if (searchForm.endTime) requestParams.endTime = searchForm.endTime
    if (searchForm.address) requestParams.address = searchForm.address
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      requestParams.cityId = searchForm.cityId.join(',')
    }

    const response = await proxy.$api.shop.orderList(requestParams)
    console.log('📋 订单列表响应:', response)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0

      console.log(`✅ 订单列表加载成功，共 ${total.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('❌ 加载订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  searchForm.pageNum = 1
  loadOrderList()
  loadCensusData() // 搜索时也重新加载统计数据
}

/**
 * 重置搜索
 */
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 10,
    orderCode: '',
    goodsName: '',
    coachName: '',
    payType: null,
    type: null,
    startTime: '',
    endTime: '',
    address: '',
    cityId: []
  })
  timeRange.value = []
  loadOrderList()
  loadCensusData() // 重置时也重新加载统计数据
}

/**
 * 分页大小变化
 */
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  loadOrderList()
}

/**
 * 当前页变化
 */
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  loadOrderList()
}

/**
 * 导出订单列表 - POST方法，传递JSON body参数
 */
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出订单Excel...')

    // 构建导出参数JSON对象
    const exportParams = {}

    // 添加订单编号
    if (searchForm.orderCode !== '' && searchForm.orderCode !== null && searchForm.orderCode !== undefined) {
      exportParams.orderCode = searchForm.orderCode
    }

    // 添加商品名称
    if (searchForm.goodsName !== '' && searchForm.goodsName !== null && searchForm.goodsName !== undefined) {
      exportParams.goodsName = searchForm.goodsName
    }

    // 添加师傅名称
    if (searchForm.coachName !== '' && searchForm.coachName !== null && searchForm.coachName !== undefined) {
      exportParams.coachName = searchForm.coachName
    }

    // 添加支付类型
    if (searchForm.payType !== '' && searchForm.payType !== null && searchForm.payType !== undefined) {
      exportParams.payType = parseInt(searchForm.payType)
    }

    // 添加订单类型
    if (searchForm.type !== '' && searchForm.type !== null && searchForm.type !== undefined) {
      exportParams.type = parseInt(searchForm.type)
    }

    // 添加地址
    if (searchForm.address !== '' && searchForm.address !== null && searchForm.address !== undefined) {
      exportParams.address = searchForm.address
    }

    // 添加城市ID
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      exportParams.cityId = searchForm.cityId.join(',')
    }

    // 添加时间范围
    if (searchForm.startTime !== '' && searchForm.startTime !== null && searchForm.startTime !== undefined) {
      exportParams.startTime = searchForm.startTime
    }

    if (searchForm.endTime !== '' && searchForm.endTime !== null && searchForm.endTime !== undefined) {
      exportParams.endTime = searchForm.endTime
    }

    // 添加分页参数（根据接口要求）
    exportParams.pageNum = 1
    exportParams.pageSize = 10

    console.log('📤 导出参数:', exportParams)

    // 使用fetch发送POST请求下载文件 - 使用环境变量
    const token = sessionStorage.getItem('minitk')
    const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
    const exportUrl = `${baseUrl}/api/admin/order/export`

    console.log('📤 导出URL:', exportUrl)

    const response = await fetch(exportUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify(exportParams)
    })

    if (response.ok) {
      // 检查响应内容类型
      const contentType = response.headers.get('Content-Type')

      // 如果是JSON响应，说明可能是错误信息
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json()
        console.error('❌ 导出返回错误:', errorData)

        if (errorData.code === '-1' || errorData.code === -1) {
          // 显示具体的错误信息
          const errorMsg = errorData.msg || '导出失败'
          ElMessage.error(`导出失败: ${errorMsg}`)

          // 如果是数据库字段映射错误，给出更友好的提示
          if (errorMsg.includes('coachMobile') || errorMsg.includes('ResultMapException')) {
            ElMessage.warning('后端数据库字段映射异常，请联系技术人员修复')
          }
        } else {
          ElMessage.error(errorData.msg || '导出失败')
        }
        return
      }

      // 获取文件名（从响应头或使用默认名称）
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `订单导出_${new Date().toISOString().slice(0, 10)}.xlsx`

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 创建blob并下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功，请查看浏览器下载')
      console.log('✅ 导出订单Excel成功')
    } else {
      // 尝试解析错误响应
      try {
        const errorText = await response.text()
        console.error('❌ 导出HTTP错误:', response.status, response.statusText, errorText)

        // 尝试解析JSON错误信息
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.msg) {
            ElMessage.error(`导出失败: ${errorData.msg}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        } catch (parseError) {
          throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
        }
      } catch (textError) {
        throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
      }
    }

  } catch (error) {
    console.error('❌ 导出订单Excel异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 查看订单详情
 */
const handleViewDetail = async (row) => {
  try {
    console.log('📄 查看订单详情:', row.id)

    const response = await proxy.$api.shop.orderDetail(row.id)
    console.log('📄 订单详情响应:', response)

    if (response.code === '200') {
      orderDetail.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('❌ 获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

/**
 * 预览图片（兼容原有调用）
 */
const previewImage = (imageList, index = 0) => {
  try {
    console.log('🖼️ 预览图片:', imageList, index)

    if (!imageList || imageList.length === 0) {
      ElMessage.warning('没有可预览的图片')
      return
    }

    // 确保索引有效
    const validIndex = Math.max(0, Math.min(index, imageList.length - 1))
    const targetImage = imageList[validIndex]

    if (!isImageUrl(targetImage)) {
      ElMessage.error('无效的图片链接')
      return
    }

    // 使用新的预览方法
    handleImagePreview(targetImage)

  } catch (error) {
    console.error('❌ 预览图片失败:', error)
    ElMessage.error('预览图片失败')
  }
}

/**
 * 下载单张图片
 */
const downloadImage = async (imageUrl, index) => {
  try {
    console.log('⬇️ 下载图片:', imageUrl, index)

    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a')
    link.href = imageUrl
    link.download = `订单图片_${index + 1}_${Date.now()}.jpg`
    link.target = '_blank'

    // 添加到DOM并触发点击
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('图片下载已开始')
  } catch (error) {
    console.error('❌ 下载图片失败:', error)
    ElMessage.error('下载图片失败')
  }
}

/**
 * 预览所有图片
 */
const previewAllImages = () => {
  try {
    if (orderDetail.value?.goodsImages && orderDetail.value.goodsImages.length > 0) {
      console.log('🖼️ 预览所有图片:', orderDetail.value.goodsImages.length)
      // 触发第一张图片的预览，用户可以通过预览器浏览所有图片
      previewImage(orderDetail.value.goodsImages, 0)
    } else {
      ElMessage.warning('没有可预览的图片')
    }
  } catch (error) {
    console.error('❌ 预览所有图片失败:', error)
    ElMessage.error('预览图片失败')
  }
}

/**
 * 下载所有图片
 */
const downloadAllImages = async () => {
  try {
    const images = orderDetail.value?.goodsImages || []
    if (images.length === 0) {
      ElMessage.warning('没有可下载的图片')
      return
    }

    console.log('⬇️ 批量下载图片:', images.length)

    // 逐个下载图片，避免浏览器阻止多个下载
    for (let i = 0; i < images.length; i++) {
      setTimeout(() => {
        downloadImage(images[i], i)
      }, i * 500) // 每500ms下载一张，避免浏览器限制
    }

    ElMessage.success(`开始下载 ${images.length} 张图片`)
  } catch (error) {
    console.error('❌ 批量下载图片失败:', error)
    ElMessage.error('批量下载图片失败')
  }
}

/**
 * 复制图片链接
 */
const copyImageUrls = async () => {
  try {
    const images = orderDetail.value?.goodsImages || []
    if (images.length === 0) {
      ElMessage.warning('没有可复制的图片链接')
      return
    }

    const urlsText = images.join('\n')

    // 使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(urlsText)
    } else {
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea')
      textArea.value = urlsText
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }

    console.log('📋 复制图片链接成功:', images.length)
    ElMessage.success(`已复制 ${images.length} 个图片链接到剪贴板`)
  } catch (error) {
    console.error('❌ 复制图片链接失败:', error)
    ElMessage.error('复制图片链接失败')
  }
}

/**
 * 复制单个内容到剪贴板
 */
const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
    } else {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }

    console.log('📋 复制成功:', text.substring(0, 50) + '...')
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('❌ 复制失败:', error)
    ElMessage.error('复制失败')
  }
}

/**
 * 判断是否为图片URL
 */
const isImageUrl = (url) => {
  if (!url || typeof url !== 'string') return false

  // 支持 http/https 协议
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // 检查常见图片扩展名
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico']
    const lowerUrl = url.toLowerCase()
    return imageExtensions.some(ext => lowerUrl.includes(ext)) ||
      lowerUrl.includes('image') ||
      lowerUrl.includes('img') ||
      lowerUrl.includes('picture') ||
      lowerUrl.includes('photo')
  }

  // 支持 data: 协议的 base64 图片
  if (url.startsWith('data:image/')) {
    return true
  }

  // 支持相对路径图片
  if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico']
    const lowerUrl = url.toLowerCase()
    return imageExtensions.some(ext => lowerUrl.endsWith(ext))
  }

  return false
}

/**
 * 获取图片类型标签
 */
const getImageTypeLabel = (url) => {
  if (!url) return '图片'

  const lowerUrl = url.toLowerCase()
  if (lowerUrl.includes('.jpg') || lowerUrl.includes('.jpeg')) return 'JPEG图片'
  if (lowerUrl.includes('.png')) return 'PNG图片'
  if (lowerUrl.includes('.gif')) return 'GIF图片'
  if (lowerUrl.includes('.webp')) return 'WebP图片'
  if (lowerUrl.includes('.svg')) return 'SVG图片'
  if (lowerUrl.includes('.bmp')) return 'BMP图片'
  if (url.startsWith('data:image/')) return 'Base64图片'

  return '上传图片'
}

/**
 * 处理图片预览（防止闪烁）
 */
const handleImagePreview = (imageUrl) => {
  try {
    console.log('🖼️ 预览图片:', imageUrl)

    if (!isImageUrl(imageUrl)) {
      ElMessage.error('无效的图片链接')
      return
    }

    // 预加载图片，确保能正常显示
    const img = new Image()
    img.onload = () => {
      console.log('✅ 图片预加载成功，开始预览')

      // 创建临时的 el-image 元素来触发预览
      const tempImageContainer = document.createElement('div')
      tempImageContainer.style.position = 'fixed'
      tempImageContainer.style.top = '-9999px'
      tempImageContainer.style.left = '-9999px'
      tempImageContainer.style.opacity = '0'
      tempImageContainer.style.pointerEvents = 'none'

      const tempImage = document.createElement('img')
      tempImage.src = imageUrl
      tempImage.style.width = '1px'
      tempImage.style.height = '1px'

      // 添加点击事件来触发预览
      tempImage.addEventListener('click', () => {
        // 使用 Element Plus 的图片预览
        const previewContainer = document.createElement('div')
        previewContainer.innerHTML = `
          <el-image
            src="${imageUrl}"
            :preview-src-list="['${imageUrl}']"
            :z-index="10001"
            :hide-on-click-modal="false"
            :preview-teleported="true"
            style="display: none;"
          ></el-image>
        `
        document.body.appendChild(previewContainer)

        // 触发预览
        const elImage = previewContainer.querySelector('img')
        if (elImage) {
          elImage.click()
        }

        // 清理临时元素
        setTimeout(() => {
          document.body.removeChild(tempImageContainer)
          document.body.removeChild(previewContainer)
        }, 1000)
      })

      tempImageContainer.appendChild(tempImage)
      document.body.appendChild(tempImageContainer)

      // 延迟触发点击，避免事件冲突
      setTimeout(() => {
        tempImage.click()
      }, 50)
    }

    img.onerror = () => {
      console.error('❌ 图片预加载失败')
      ElMessage.error('图片加载失败，无法预览')
    }

    img.src = imageUrl

  } catch (error) {
    console.error('❌ 预览图片失败:', error)
    ElMessage.error('预览图片失败')
  }
}

/**
 * 重试加载图片
 */
const retryLoadImage = (imageUrl) => {
  try {
    console.log('🔄 重试加载图片:', imageUrl)
    // 强制刷新图片，添加时间戳避免缓存
    const timestamp = new Date().getTime()
    const newUrl = imageUrl.includes('?') ? `${imageUrl}&t=${timestamp}` : `${imageUrl}?t=${timestamp}`

    // 重新加载图片
    const img = new Image()
    img.onload = () => {
      console.log('✅ 图片重新加载成功')
      ElMessage.success('图片加载成功')
    }
    img.onerror = () => {
      console.error('❌ 图片重新加载失败')
      ElMessage.error('图片加载失败，请检查图片链接')
    }
    img.src = newUrl

  } catch (error) {
    console.error('❌ 重试加载图片失败:', error)
    ElMessage.error('重试失败')
  }
}



/**
 * 获取图片尺寸信息（可选功能）
 */
const getImageSize = (imageUrl) => {
  // 这里可以实现获取图片尺寸的逻辑
  // 由于需要异步加载图片，这里先返回空，可以后续优化
  console.log('获取图片尺寸:', imageUrl)
  return null
}



/**
 * 加载排行榜数据
 */
const loadRankData = async () => {
  try {
    rankLoading.value = true
    console.log('📊 加载排行榜数据，当前标签:', activeRankTab.value)

    const params = {
      top: rankForm.top,
      startTime: rankForm.startTime,
      endTime: rankForm.endTime
    }

    if (activeRankTab.value === 'coachIncome') {
      const response = await proxy.$api.shop.coachIncomeRank(params)
      if (response.code === '200') {
        coachIncomeRank.value = response.data || []
        console.log('💰 师傅收入排行榜加载成功:', coachIncomeRank.value.length)
      }
    } else if (activeRankTab.value === 'coachCancel') {
      const response = await proxy.$api.shop.coachCancelRank(params)
      if (response.code === '200') {
        coachCancelRank.value = response.data || []
        console.log('🏃 师傅跑单排行榜加载成功:', coachCancelRank.value.length)
      }
    } else if (activeRankTab.value === 'userCancel') {
      const response = await proxy.$api.shop.userCancelRank(params)
      if (response.code === '200') {
        userCancelRank.value = response.data || []
        console.log('👤 用户跑单排行榜加载成功:', userCancelRank.value.length)
      }
    }
  } catch (error) {
    console.error('❌ 加载排行榜数据失败:', error)
    ElMessage.error('加载排行榜数据失败')
  } finally {
    rankLoading.value = false
  }
}

/**
 * 排行榜标签切换
 */
const handleRankTabClick = (tab) => {
  console.log('🔄 切换排行榜标签:', tab.props.name)
  activeRankTab.value = tab.props.name
  loadRankData()
}

/**
 * 查看报价详情
 */
const handleViewQuoteDetail = async (row) => {
  try {
    console.log('💰 查看报价详情:', row)
    console.log('💰 订单ID:', row.id)
    console.log('💰 quotedPriceVos值:', row.quotedPriceVos)

    if (!row.id) {
      ElMessage.error('订单ID不能为空')
      return
    }

    currentOrderId.value = row.id
    quoteForm.id = row.id
    quoteForm.pageNum = 1
    quoteForm.pageSize = 10

    console.log('💰 开始加载报价详情数据...')
    await loadQuoteDetailData()
    quoteDetailDialogVisible.value = true
    console.log('💰 报价详情弹窗已打开')
  } catch (error) {
    console.error('❌ 查看报价详情失败:', error)
    ElMessage.error(`查看报价详情失败: ${error.message || error}`)
  }
}

/**
 * 查看补差价详情
 */
const handleViewDiffPriceDetail = async (row) => {
  try {
    console.log('💸 查看补差价详情:', row)
    console.log('💸 订单ID:', row.id)
    console.log('💸 orderDiffPrices值:', row.orderDiffPrices)

    if (!row.id) {
      ElMessage.error('订单ID不能为空')
      return
    }

    currentOrderId.value = row.id
    diffPriceForm.id = row.id
    diffPriceForm.pageNum = 1
    diffPriceForm.pageSize = 10

    console.log('💸 开始加载补差价详情数据...')
    await loadDiffPriceDetailData()
    diffPriceDialogVisible.value = true
    console.log('💸 补差价详情弹窗已打开')
  } catch (error) {
    console.error('❌ 查看补差价详情失败:', error)
    ElMessage.error(`查看补差价详情失败: ${error.message || error}`)
  }
}

/**
 * 加载报价详情数据
 */
const loadQuoteDetailData = async () => {
  try {
    quoteLoading.value = true
    console.log('💰 加载报价详情数据，参数:', quoteForm)

    const response = await proxy.$api.shop.quotedInfo({
      id: quoteForm.id,
      pageNum: quoteForm.pageNum,
      pageSize: quoteForm.pageSize
    })

    console.log('💰 报价详情响应:', response)

    if (response.code === '200') {
      quoteDetailList.value = response.data.list || []
      quoteTotal.value = response.data.totalCount || 0
      console.log(`✅ 报价详情加载成功，共 ${quoteTotal.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取报价详情失败')
    }
  } catch (error) {
    console.error('❌ 加载报价详情失败:', error)
    ElMessage.error('获取报价详情失败')
  } finally {
    quoteLoading.value = false
  }
}

/**
 * 加载补差价详情数据
 */
const loadDiffPriceDetailData = async () => {
  try {
    diffPriceLoading.value = true
    console.log('💸 加载补差价详情数据，参数:', diffPriceForm)

    const response = await proxy.$api.shop.diffPricesInfo({
      id: diffPriceForm.id,
      pageNum: diffPriceForm.pageNum,
      pageSize: diffPriceForm.pageSize
    })

    console.log('💸 补差价详情响应:', response)

    if (response.code === '200') {
      diffPriceDetailList.value = response.data.list || []
      diffPriceTotal.value = response.data.totalCount || 0
      console.log(`✅ 补差价详情加载成功，共 ${diffPriceTotal.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取补差价详情失败')
    }
  } catch (error) {
    console.error('❌ 加载补差价详情失败:', error)
    ElMessage.error('获取补差价详情失败')
  } finally {
    diffPriceLoading.value = false
  }
}

/**
 * 报价详情分页大小变化
 */
const handleQuoteSizeChange = (size) => {
  quoteForm.pageSize = size
  quoteForm.pageNum = 1
  loadQuoteDetailData()
}

/**
 * 报价详情当前页变化
 */
const handleQuoteCurrentChange = (page) => {
  quoteForm.pageNum = page
  loadQuoteDetailData()
}

/**
 * 补差价详情分页大小变化
 */
const handleDiffPriceSizeChange = (size) => {
  diffPriceForm.pageSize = size
  diffPriceForm.pageNum = 1
  loadDiffPriceDetailData()
}

/**
 * 补差价详情当前页变化
 */
const handleDiffPriceCurrentChange = (page) => {
  diffPriceForm.pageNum = page
  loadDiffPriceDetailData()
}

/**
 * 获取补差价状态文本
 */
const getDiffPriceStatusText = (status) => {
  const statusMap = {
    '-1': '已取消',
    '0': '待确认',
    '1': '已确认待支付',
    '2': '已支付',
    '3': '已拒绝'
  }
  return statusMap[status] || '未知'
}

/**
 * 获取补差价状态标签类型
 */
const getDiffPriceStatusType = (status) => {
  const typeMap = {
    '-1': 'danger',    // 已取消
    '0': 'warning',    // 待确认
    '1': 'primary',    // 已确认待支付
    '2': 'success',    // 已支付
    '3': 'danger'      // 已拒绝
  }
  return typeMap[status] || 'info'
}

/**
 * 获取退款状态文本
 */
const getRefundStatusText = (refundType) => {
  const statusMap = {
    '0': '未申请',
    '1': '退款成功',
    '2': '退款申请中',
    '3': '拒绝退款'
  }
  return statusMap[refundType] || '未知'
}

/**
 * 获取退款状态标签类型
 */
const getRefundStatusType = (refundType) => {
  const typeMap = {
    '0': 'info',       // 未申请
    '1': 'success',    // 退款成功
    '2': 'warning',    // 退款申请中
    '3': 'danger'      // 拒绝退款
  }
  return typeMap[refundType] || 'info'
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 订单管理页面初始化')
  loadOrderList()
  loadCensusData()
  getCityData()
})
</script>

<style scoped>
/* ===== 核心样式实现 ===== */

/* 1. 页面容器 - 基础布局 */
.page-container {
  padding: 0px;
}

/* 2. 内容容器 - 白色背景 + 圆角 + 阴影 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
}

/* 2.1 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stats-content {
  text-align: center;
  padding: 10px 0;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 2.2 详细统计卡片样式 */
.detail-stats-cards {
  margin-bottom: 20px;
}

.detail-stats-card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.detail-stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  padding: 10px 0;
}

.stat-item {
  text-align: center;
  padding: 10px;
  border-radius: 6px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 3. 搜索表单 - 灰色背景区域 */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 4. 表格容器 - 关键的阴影和圆角效果 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 5. 表格样式 - 深度选择器覆盖Element Plus默认样式 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

/* 表头样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

/* 表格内容样式 */
.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

/* 行悬停效果 */
.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 6. 价格文本样式 */
.price-text {
  color: #e6a23c;
  font-weight: 600;
}

.income-text {
  color: #67c23a;
  font-weight: 600;
  font-size: 16px;
}

/* 7. 订单详情样式 */
.order-detail-dialog .el-dialog__body {
  padding: 20px 24px;
}

.detail-content {
  padding: 0;
}

/* 订单概览卡片 */
.order-overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.order-number .label {
  font-size: 14px;
  opacity: 0.9;
  margin-right: 8px;
}

.order-number .value {
  font-size: 18px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.order-status .el-tag {
  font-size: 14px;
  padding: 8px 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.order-meta {
  display: flex;
  gap: 32px;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.meta-item i {
  font-size: 16px;
  opacity: 0.9;
}

.price-highlight {
  font-size: 20px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 主要信息区域 */
.main-info-row {
  margin-bottom: 24px;
}

/* 信息卡片 */
.info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-header {
  background: #f8f9fa;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header i {
  font-size: 18px;
  color: #667eea;
}

.card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.card-content {
  padding: 20px;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item .label {
  font-size: 12px;
  color: #8492a6;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

/* 增强的商品项目样式 */
.goods-item-enhanced {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.goods-item-enhanced:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.goods-main-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.goods-image-wrapper {
  margin-right: 16px;
  flex-shrink: 0;
}

.goods-cover-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.goods-basic-info {
  flex: 1;
}

.goods-name-enhanced {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.goods-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.goods-quantity-enhanced {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.goods-price-enhanced {
  font-size: 18px;
  font-weight: 700;
  color: #e6a23c;
}

.goods-settings-enhanced {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.settings-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.settings-title::before {
  content: '';
  width: 3px;
  height: 14px;
  background: #667eea;
  margin-right: 8px;
  border-radius: 2px;
}

.settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.setting-item-enhanced {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-label-enhanced {
  font-size: 12px;
  color: #8492a6;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.setting-value-enhanced {
  display: flex;
  align-items: center;
}

.setting-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 增强的物品上传图片样式 */
.setting-image-enhanced {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 200px;
  transition: all 0.3s ease;
}

.setting-image-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.setting-image-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.setting-image-display {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.setting-image-wrapper:hover .setting-image-display {
  transform: scale(1.1);
}

.setting-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.setting-image-wrapper:hover .setting-image-overlay {
  opacity: 1;
}

.setting-image-actions {
  display: flex;
  gap: 6px;
}

.setting-image-actions .el-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  backdrop-filter: blur(4px);
  width: 28px;
  height: 28px;
}

.setting-image-actions .el-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.setting-image-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.image-label {
  font-size: 11px;
  color: #8492a6;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.image-url-preview {
  font-size: 12px;
  color: #667eea;
  cursor: pointer;
  word-break: break-all;
  line-height: 1.3;
  padding: 4px 8px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.image-url-preview:hover {
  background: rgba(102, 126, 234, 0.2);
  color: #5a6fd8;
}

/* 物品上传图片的加载状态 */
.setting-image-placeholder,
.setting-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f7fa;
  color: #8492a6;
  font-size: 11px;
}

.setting-image-placeholder i,
.setting-image-error i {
  font-size: 20px;
  margin-bottom: 4px;
  opacity: 0.6;
}

.setting-image-error {
  background: #fef0f0;
  color: #f56c6c;
}

.goods-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.summary-text {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background: #f8f9fa;
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
}

/* 增强的物品图片展示样式 */
.image-count-badge {
  margin-left: auto;
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.images-gallery-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.gallery-item-enhanced {
  position: relative;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.gallery-item-enhanced:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.image-wrapper {
  position: relative;
  overflow: hidden;
}

.gallery-image-enhanced {
  width: 100%;
  height: 160px;
  transition: transform 0.3s ease;
}

.gallery-item-enhanced:hover .gallery-image-enhanced {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item-enhanced:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.image-actions .el-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  backdrop-filter: blur(4px);
}

.image-actions .el-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.image-info {
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.image-index {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
}

.image-size {
  font-size: 11px;
  color: #8492a6;
}

/* 图片操作工具栏 */
.image-toolbar {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.image-toolbar .el-button {
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
}

/* 单独图片容器样式 */
.single-image-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.single-image-wrapper {
  position: relative;
  display: inline-block;
  max-width: 300px;
  margin: 0 auto;
}

.single-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.single-image-actions {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.single-image-wrapper:hover .single-image-actions {
  opacity: 1;
}

.single-image-actions .el-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  backdrop-filter: blur(4px);
}

.single-image-url {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 13px;
}

.url-label {
  font-weight: 500;
  color: #333;
  flex-shrink: 0;
}

.url-text {
  color: #667eea;
  cursor: pointer;
  word-break: break-all;
  flex: 1;
  transition: color 0.2s ease;
}

.url-text:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

/* 图片加载状态样式 */
.image-placeholder,
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 160px;
  background: #f5f7fa;
  color: #8492a6;
  font-size: 12px;
}

.image-placeholder i,
.image-error i {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.image-error {
  background: #fef0f0;
  color: #f56c6c;
}

/* 价格文本样式 */
.price-text {
  color: #e6a23c;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .order-detail-dialog .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .main-info-row .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .order-overview-card {
    padding: 16px;
  }

  .order-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .order-meta {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .goods-main-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .goods-image-wrapper {
    margin-right: 0;
    align-self: center;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .images-gallery {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }

  .gallery-image {
    height: 100px;
  }
}

/* 项目配置中的物品图片容器样式 */
.goods-image-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  margin-top: 5px;
}

.image-url-display {
  font-size: 11px;
  color: #999;
  word-break: break-all;
  max-width: 250px;
  line-height: 1.3;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* 修复图片预览闪烁问题 */
:deep(.el-image-viewer__wrapper) {
  z-index: 10000 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.8) !important;
}

:deep(.el-image-viewer__mask) {
  z-index: 10001 !important;
  background: rgba(0, 0, 0, 0.8) !important;
}

:deep(.el-image-viewer__canvas) {
  z-index: 10002 !important;
}

:deep(.el-image-viewer__actions) {
  z-index: 10003 !important;
}

/* 确保弹窗内的图片预览不会被遮挡 */
:deep(.el-dialog) {
  z-index: 2000 !important;
}

:deep(.el-dialog__wrapper) {
  z-index: 2000 !important;
}

/* 图片加载状态优化 */
:deep(.el-image) {
  transition: none !important;
}

:deep(.el-image__inner) {
  transition: none !important;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* 防止图片预览时页面滚动 */
body.el-popup-parent--hidden {
  overflow: hidden !important;
}

/* 图片预览增强样式 */
:deep(.el-image-viewer__wrapper) {
  z-index: 10001 !important;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

:deep(.el-image-viewer__mask) {
  background-color: rgba(0, 0, 0, 0.8) !important;
  transition: all 0.3s ease !important;
}

:deep(.el-image-viewer__btn) {
  background-color: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

:deep(.el-image-viewer__btn:hover) {
  background-color: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1);
}

/* 图片加载状态优化 */
.setting-image-display {
  transition: all 0.3s ease !important;
  cursor: pointer;
}

.setting-image-display:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 图片操作按钮样式优化 */
.setting-image-actions .el-button {
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.setting-image-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 错误状态样式优化 */
.setting-image-error {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border: 2px dashed #d0d0d0;
  transition: all 0.3s ease;
}

.setting-image-error:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

/* 8. 排行榜容器样式 */
.rank-container {
  padding: 10px 0;
}

.rank-form {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.rank-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 9. 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button+.el-button {
  margin-left: 10px;
}

/* 10. 操作按钮样式 */
.table-operate {
  display: flex;
  gap: 5px;
  justify-content: center;
}

/* 11. 标签样式优化 */
.el-tag {
  font-size: 12px;
}

/* 11. 弹窗样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px 8px 0 0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* 12. 描述列表样式 */
:deep(.el-descriptions) {
  margin-top: 10px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #333;
}

/* 13. 标签页样式 */
:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

/* 14. 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }

  .search-form .el-form-item {
    margin-right: 10px;
  }

  .rank-form {
    padding: 12px;
  }
}

/* 15. 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}

/* 16. 报价详情和补差价详情弹窗样式 */
.quote-detail-container,
.diff-price-container {
  padding: 10px 0;
}

.quote-pagination-container,
.diff-price-pagination-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

/* 17. 操作按钮组样式优化 */
.table-operate {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.table-operate .el-button {
  margin: 0;
  font-size: 12px;
  padding: 6px 12px;
}

/* 18. 弹窗表格样式优化 */
.quote-detail-container .el-table,
.diff-price-container .el-table {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 19. 图片预览样式 */
.el-image {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.el-image:hover {
  transform: scale(1.05);
}
</style>